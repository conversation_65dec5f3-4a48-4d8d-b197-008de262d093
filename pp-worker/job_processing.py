import ctypes
import json
import storage
import database
from multiprocessing import Event
import time
import gzip
import shutil
import os
import contextlib
import logging
import subprocess
from config import task_folder, precision_folder, is_dev, bucket_name, output_bucket_name, dev_output_bucket_name, concurrent_threads_per_worker_core # Renamed job_folder -> task_folder
from output_files import OutputFileManager, OutputFile

SHUTDOWN_EVENT = None  # Global shutdown flag
DUAL_HANDLER = None  # Global dual logging handler (set by worker init)



# Renamed Job -> Task, Batch -> Job internally
class TaskProcessor: # Renamed from JobProcessor
    def __init__(self) -> None:
        self.task = None # Renamed from self.job
        # Database table name MUST remain 'jobs' as per constraint
        self.table_name = "tasks" if is_dev else "tasks" # Table for tasks (formerly jobs), aligned with database.DEFAULT_TABLE
        self.core_library = "./core/libDAC.so"
        self.output_manager = OutputFileManager()

        # Performance timing infrastructure
        self.timing_data = {
            'download_time': 0.0,
            'processing_time': 0.0,
            'upload_time': 0.0,
            'database_time': 0.0,
            'total_start_time': None
        }

    def _start_timing(self, operation_type):
        """Start timing for a specific operation type"""
        return time.time()

    def _end_timing(self, operation_type, start_time):
        """End timing for a specific operation type and accumulate the duration"""
        duration = time.time() - start_time
        self.timing_data[operation_type] += duration
        return duration

    def _get_dataset_name(self):
        """Extract dataset name from task configuration"""
        try:
            # Try to get dataset name from task vars
            task_vars = self.task.get('vars', {}).get('vars', [])
            if isinstance(task_vars, list):
                for var_def in task_vars:
                    if isinstance(var_def, dict) and var_def.get('name') == 'DSNAME':
                        return var_def.get('data', 'Unknown')

            # Fallback to task ID if no dataset name found
            return f"Task-{self.task.get('id', 'Unknown')}"
        except Exception:
            return f"Task-{self.task.get('id', 'Unknown')}"

    def _log_performance_summary(self):
        """Log consolidated performance summary for the completed task"""
        if self.timing_data['total_start_time'] is None:
            return

        total_time = time.time() - self.timing_data['total_start_time']
        task_id = self.task.get('id', 'UNKNOWN')
        dataset_name = self._get_dataset_name()

        # Format timing data
        download_time = self.timing_data['download_time']
        processing_time = self.timing_data['processing_time']
        upload_time = self.timing_data['upload_time']
        database_time = self.timing_data['database_time']

        # Log the consolidated performance summary
        logging.info(
            f"Task [{task_id}] Dataset [{dataset_name}] completed - "
            f"Download: {download_time:.1f}s, Processing: {processing_time:.1f}s, "
            f"Upload: {upload_time:.1f}s, Database: {database_time:.1f}s, "
            f"Total: {total_time:.1f}s"
        )

    def process_task(self, task_data): # Renamed from process_job, param job -> task_data
        self.task = task_data # Renamed from self.job

        # Ensure all required fields are present for backward compatibility
        if 'user_id' not in self.task:
            self.task['user_id'] = None
        if 'bulk_job_type' not in self.task:
            self.task['bulk_job_type'] = None

        # workervars seem related to the task being processed
        self.workervars = task_data.get('workervars', {})

        # Set task context for dual logging (console + task-specific file)
        task_id = self.task.get('id', 'UNKNOWN')
        global DUAL_HANDLER
        try:
            if DUAL_HANDLER:
                DUAL_HANDLER.set_task_context(task_id)
        except Exception as e:
            # If setting task context fails, continue with console logging only
            logging.warning(f"TaskProcessor: Could not set task context for logging: {e}")

        # Start total timing
        self.timing_data['total_start_time'] = time.time()
        task_was_locked = False

        # Check if task is already locked (from get_next_available_task)
        if self.task.get('status') == 'In Progress':
            # Task was already locked by get_next_available_task, skip locking
            task_was_locked = True
            logging.debug(f"Task {self.task.get('id', 'UNKNOWN')} already locked by get_next_available_task")
        else:
            # Traditional locking approach
            if not self._lock_task(): # Renamed from _lock_job
                # Task could not be locked - it was already processed by another worker
                logging.info(f"Task {self.task.get('id', 'UNKNOWN')} could not be locked - skipping (already processed by another worker)")
                return
            task_was_locked = True

        try:
            self._handle_task_based_on_type() # Renamed from _handle_job_based_on_type
            if self.task.get('result', {}).get('status') == 'Success':
                self._finalize_task() # Renamed from _finalize_job
            else:
                self._task_failed() # Renamed from _job_failed
        except KeyError as e:
            error_msg = f"Missing required field {e} in task data"
            print(f"An exception occurred processing task {self.task.get('id', 'UNKNOWN')}: {error_msg}") # Added task ID
            logging.error(f"Task {self.task.get('id', 'UNKNOWN')} KeyError: {error_msg}. Task data keys: {list(self.task.keys())}")
            self.task['result'] = {'status': 'Error', 'error_message': error_msg}
            self._task_failed() # Renamed from _job_failed
        except Exception as e:
            print(f"An exception occurred processing task {self.task.get('id', 'UNKNOWN')}: {e}") # Added task ID
            logging.error(f"Task {self.task.get('id', 'UNKNOWN')} Exception: {e}", exc_info=True)
            self.task['result'] = {'status': 'Error', 'error_message': str(e)}
            self._task_failed() # Renamed from _job_failed
        finally:
            # Clear task context when task processing is done
            global DUAL_HANDLER
            try:
                if DUAL_HANDLER:
                    DUAL_HANDLER.set_task_context(None)
            except Exception:
                pass  # Ignore cleanup errors


    def _lock_task(self): # Renamed from _lock_job
        task_id = self.task['id'] # Renamed from job_id
        # DB function MUST use old name 'job_id' (which is now task_id) and table_name 'jobs'
        # Call the renamed function in database.py
        start_time = self._start_timing('database_time')
        lock_success = database.set_task_status_with_lock(task_id, "queued", "In Progress", table_name=self.table_name)
        self._end_timing('database_time', start_time)

        if lock_success != "Success":
            # Improved logging to distinguish between different lock failure reasons
            if "could not obtain lock" in str(lock_success).lower():
                logging.debug(f"Task {task_id} lock error: {lock_success} (another worker is processing this task)")
            elif "status mismatch" in str(lock_success).lower():
                logging.debug(f"Task {task_id} lock error: {lock_success} (task was already processed)")
            else:
                logging.warning(f"Task {task_id} lock error: {lock_success}")
            return False
        return True

    def _handle_task_based_on_type(self): # Renamed from _handle_job_based_on_type
        # 'bulk_job_type' refers to the container (new 'job') type.
        container_task_type = self.task.get('bulk_job_type') # Renamed bulk_type -> container_job_type
        # 'job_json' contains task configuration. Renamed from job_json
        task_config_type = self.task.get('job_json', {}).get('type') # Renamed job_type -> task_config_type

        if container_task_type:
            # This task represents a container job (old 'batch'/'bulk_job')
            logging.info(f"Processing Container Job Task {self.task['id']} (Type: {container_task_type})") # Updated log
            # TODO: Add specific handling for different container job types if needed later
            self._process_bulk_task() # Renamed for clarity
        elif task_config_type == 'dummy':
            logging.info(f"Processing Dummy Task {self.task['id']}") # Renamed log message
            self._process_dummy_task() # Renamed from _process_dummy_job
        else: # Assuming other types are standard processing tasks
            logging.info(f"Processing Standard Task {self.task['id']}") # Renamed log message
            self._process_standard_task() # Renamed from _process_standard_job

    def _process_dummy_task(self): # Renamed from _process_dummy_job
        task_id = self.task.get('id', 'UNKNOWN') # Use task_id
        print(f"Processing Dummy task {task_id}") # Added task ID
        for _ in range(10):
            if SHUTDOWN_EVENT and SHUTDOWN_EVENT.is_set():
                print(f"Shutdown signal received. Exiting dummy task {task_id}.") # Added task ID
                return
            time.sleep(1)  # Simulate work
        print(f"Dummy task {task_id} completed") # Added task ID

    def _process_standard_task(self): # Renamed from _process_standard_job
        self._setup_storage() # Uses self.task['id'] internally now
        self._setup_output_files() # Uses self.task internally now
        self._process_files() # Uses self.task internally now
        result = self._run_core() # Uses self.task internally now
        self.task['result'] = result # Renamed self.job -> self.task
        self._store_output_files() # Uses self.task internally now
        self._log_performance_summary()
        if not self._validate_output_files(): # Uses self.task internally now
            return

    # --- Bulk Container Job Processing Methods Start ---
    # These methods handle the container (the new 'job'), which was previously called 'bulk_job'

    def _process_bulk_task(self):
        """Orchestrates the processing steps for bulk container jobs (new 'job', old 'batch'/'bulk_job')."""
        task_id = self.task['id'] # The ID of the container job itself (which is stored in the task structure)
        try:
            self._setup_storage_for_bulk() # Uses self.task['id'] (job_id)
            self._setup_output_files() # Defines the output of the bulk job itself, uses self.task['id'] (job_id)
            self._process_bulk_input_files() # Fetches results of previous tasks as input, uses self.task['id'] (job_id)
            result = self._run_core() # Needs vars populated correctly by previous step, uses self.task['id'] (job_id)
            self.task['result'] = result # Store result in the container job's task structure
            self._store_output_files() # Stores the final output of the bulk job, uses self.task['id'] (job_id)
            self._log_performance_summary() # Log performance timing data for bulk tasks
            if not self._validate_output_files(): # Validates the final output, uses self.task['id'] (job_id)
                # Error is set within _validate_output_files
                return
        except Exception as e:
            logging.error(f"Error processing bulk task {task_id}: {e}", exc_info=True) # Updated log
            self.task['result'] = {'status': 'Error', 'error_message': f"Bulk container job processing failed: {str(e)}"} # Updated log
            # _task_failed() will be called by the main process_task handler for the container job's task

    def _setup_storage_for_bulk(self):
        """Sets up storage access for bulk container jobs (reading previous task results, writing job outputs)."""
        task_id = self.task['id'] # ID of the container job
        task_path_base = f"{task_folder}/{task_id}" # Path for the container job

        storage_class = self.get_storage_class('supabase_file')
        if storage_class is None:
            raise ValueError("Unknown storage type 'supabase_file'")

        # Storage for reading input result files (from previous tasks) from the output bucket
        logging.info(f"Task {task_id}: Setting up task result input store from bucket: {output_bucket_name}") # Updated log
        self.result_input_store = storage_class(output_bucket_name, task_path_base, "/") # Read from root relative to bucket

        # Storage for writing final outputs of this container job to the output bucket
        logging.info(f"Task {task_id}: Setting up output store to bucket: {output_bucket_name}") # Updated log
        self.outputstore = storage_class(output_bucket_name, task_path_base, "/output")

        # Storage for writing dev outputs (if applicable) for this container job
        if dev_output_bucket_name:
             logging.info(f"Task {task_id}: Setting up dev output store to bucket: {dev_output_bucket_name}") # Updated log
             self.devoutputstore = storage_class(dev_output_bucket_name, task_path_base, "/devoutput")
        else:
            logging.warning(f"Task {task_id}: Dev output bucket name not configured.") # Updated log
            self.devoutputstore = None # Or a dummy storage that does nothing

    def _process_bulk_input_files(self):
        """
        Fetches REQUIRED result files from previous tasks based on job_json['required_files'],
        downloads them, decompresses them, and updates the corresponding variable definitions
        in self.task['vars']['vars'] with the local file paths, preserving the original
        single-item vs. list-of-items structure. Operates on the container job's task data.
        """
        task_id = self.task['id'] # ID of the container job
        task_path = f"{task_folder}/{task_id}" # Path for the container job
        os.makedirs(task_path, exist_ok=True)
        logging.info(f"Processing bulk inputs for container task {task_id} in {task_path}") # Updated log

        # 1. Get Required File Variable Names from job_json (part of the container job's task data)
        if not isinstance(self.task, dict):
             # This should ideally not happen if the task fetch logic is correct
             raise TypeError(f"Task data (self.task) is not a dictionary for container task {task_id}. Type: {type(self.task)}") # Updated log

        job_json = self.task.get('job_json') # Renamed job_json -> job_json
        if not isinstance(job_json, dict):
             logging.warning(f"job_json is missing or not a dictionary for container task {task_id}. Type: {type(job_json)}. Assuming no required files.") # Updated log
             job_json = {} # Default to empty dict to avoid further errors

        required_file_var_names = job_json.get('required_files', [])
        if not isinstance(required_file_var_names, list):
            logging.error(f"Invalid 'required_files' format in job_json for container task {task_id}. Expected list, got {type(required_file_var_names)}. Assuming no files required.") # Updated log
            required_file_var_names = []
        if not required_file_var_names:
            logging.info(f"No 'required_files' specified in job_json for container task {task_id}. No input files will be downloaded.") # Updated log
            return # Nothing to do if no files are required

        logging.info(f"Container Task {task_id}: Required input file variables (from previous tasks): {required_file_var_names}") # Updated log

        # 2. Get Workervars (containing previous task IDs) and Filter/Process Required Ones
        # Also ensure self.task is a dict before accessing workervars
        if not isinstance(self.task, dict):
             raise TypeError(f"Task data (self.task) is not a dictionary when accessing workervars for container task {task_id}.") # Updated log

        # workervars contain the IDs of the previous tasks whose results are needed
        workervars = self.task.get('workervars', [])
        if not isinstance(workervars, list):
            # Allow empty list, but raise error for other non-list types
            if workervars is not None:
                 raise ValueError(f"Invalid 'workervars' format for container task {task_id}. Expected list, got {type(workervars)}.") # Updated log
            else:
                 workervars = [] # Treat None as empty list


        required_task_result_ids_to_fetch = set() # Renamed required_result_ids_to_fetch
        # {var_name: (original_data_structure_with_task_ids, original_links_list)}
        required_vars_data_structure = {}

        for var_item in workervars:
            if not isinstance(var_item, dict):
                logging.warning(f"Task {task_id}: Skipping invalid workervar item (not a dict): {var_item}") # Updated log
                continue
            var_name = var_item.get('name')
            var_data = var_item.get('data') # This contains the IDs of previous tasks
            var_links = var_item.get('links', []) # Get links, default to empty list
            if not var_name or var_data is None: # Allow empty list for data
                logging.warning(f"Task {task_id}: Skipping invalid workervar item (missing name or data): {var_item}") # Updated log
                continue

            if var_name in required_file_var_names:
                logging.info(f"Task {task_id}: Processing required workervar: {var_name}") # Updated log
                # Store original structure and links
                required_vars_data_structure[var_name] = (var_data, var_links)

                if not isinstance(var_data, list):
                    logging.warning(f"Task {task_id}: Workervar '{var_name}' data is not a list ({type(var_data)}). Skipping task ID collection for this var.") # Updated log
                    continue

                # Collect task IDs, handling nested lists
                for element in var_data:
                    if isinstance(element, (int, float)): # Allow float for flexibility, convert later
                        try:
                            required_task_result_ids_to_fetch.add(int(element)) # Renamed set
                        except (ValueError, TypeError):
                             logging.warning(f"Task {task_id}: Invalid numeric task ID '{element}' in workervar '{var_name}'. Skipping.") # Updated log
                    elif isinstance(element, list):
                        for sub_element in element:
                             if isinstance(sub_element, (int, float)):
                                 try:
                                     required_task_result_ids_to_fetch.add(int(sub_element)) # Renamed set
                                 except (ValueError, TypeError):
                                      logging.warning(f"Task {task_id}: Invalid numeric task ID '{sub_element}' in nested list for workervar '{var_name}'. Skipping.") # Updated log
                             else:
                                 logging.warning(f"Task {task_id}: Non-numeric item '{sub_element}' found in nested list for workervar '{var_name}'. Skipping.") # Updated log
                    else:
                         logging.warning(f"Task {task_id}: Non-numeric/non-list item '{element}' found in data for workervar '{var_name}'. Skipping.") # Updated log

        # 3. Check if any required task result files need fetching
        if not required_task_result_ids_to_fetch: # Renamed set
            logging.info(f"Task {task_id}: No valid task result file IDs found for the required variables: {required_file_var_names}. No files to download.") # Updated log
            # Check if the required_vars_data_structure is also empty. If not, it means the required vars exist but have no valid IDs.
            # This might be an error state depending on expectations. For now, we just return.
            return

        logging.info(f"Task {task_id}: Found {len(required_task_result_ids_to_fetch)} unique required task result file IDs to fetch: {required_task_result_ids_to_fetch}") # Updated log

        # 4. Query Database for Paths of Required Task Result Files
        # DB function MUST use old name 'result_id' (which is task_id here)
        db_start_time = self._start_timing('database_time')
        task_result_paths_map = database.get_task_result_file_paths(list(required_task_result_ids_to_fetch)) # Renamed result_paths_map, uses renamed set
        self._end_timing('database_time', db_start_time)
        # result_id (in map keys) is actually task_id

        # Verify all required task IDs were found in DB
        missing_task_ids = required_task_result_ids_to_fetch - set(task_result_paths_map.keys()) # Renamed missing_ids, uses renamed set
        if missing_task_ids:
            raise FileNotFoundError(f"Task {task_id}: Could not find database entries for required task result IDs: {missing_task_ids}") # Updated log

        # 5. Prepare Download Map (Remote Path -> Unique Local Path) for Required Task Result Files
        task_result_id_to_local_path = {} # Map task result ID to its *final* local path after potential decompression. Renamed result_id_to_local_path
        remote_to_local_map = {}     # Map remote path to initial unique local download path

        for task_result_id, remote_path in task_result_paths_map.items(): # Renamed result_id -> task_result_id
            original_filename = os.path.basename(remote_path)
            safe_original_filename = "".join(c if c.isalnum() or c in ['.', '_', '-'] else '_' for c in original_filename)
            # Include task_result_id in local name for uniqueness
            local_filename = f"{task_result_id}_{safe_original_filename}" # Unique local name based on task result ID
            local_path = os.path.join(task_path, local_filename) # Path within the container job's folder

            task_result_id_to_local_path[task_result_id] = local_path # Store initial local path in renamed map
            remote_to_local_map[remote_path] = local_path

        # 6. Download Required Task Result Files
        logging.info(f"Task {task_id}: Attempting to download {len(remote_to_local_map)} required task result files...") # Updated log
        # Time the bulk download operation
        download_start_time = self._start_timing('download_time')
        # Assumes result_input_store is configured correctly for the output bucket where task results are stored
        successfully_downloaded_local_paths = self.result_input_store.get_files_bulk(remote_to_local_map)
        self._end_timing('download_time', download_start_time)
        downloaded_local_files_set = set(successfully_downloaded_local_paths)

        # Verify downloads
        expected_local_paths_set = set(remote_to_local_map.values())
        missing_local_files = expected_local_paths_set - downloaded_local_files_set
        if missing_local_files:
             missing_info = []
             for task_res_id, expected_loc_path in task_result_id_to_local_path.items(): # Use renamed map
                 if expected_loc_path in missing_local_files:
                     remote_p = task_result_paths_map.get(task_res_id, 'N/A') # Use renamed map
                     missing_info.append(f"Task ID {task_res_id} (remote: {remote_p}, expected_local: {expected_loc_path})") # Updated log
             raise FileNotFoundError(f"Task {task_id}: Failed to download required task result files: {', '.join(missing_info)}") # Updated log
        logging.info(f"Task {task_id}: Successfully downloaded {len(downloaded_local_files_set)} required task result files.") # Updated log

        # 7. Decompress Downloaded Task Result Files
        logging.info(f"Task {task_id}: Checking downloaded required task result files for decompression...") # Updated log
        # Use the values from the initial task_result_id_to_local_path map as the paths to check
        local_paths_to_check = list(task_result_id_to_local_path.values()) # Use renamed map
        decompression_map = self.decompress_files(local_paths_to_check, task_path) # Returns {original_abs_path: final_abs_path}

        # Update task_result_id_to_local_path with the final paths after decompression
        updated_count = 0
        for task_res_id, original_local_path in list(task_result_id_to_local_path.items()): # Use renamed map
            final_path = decompression_map.get(original_local_path)
            # Check if decompression happened and path changed
            if final_path and final_path != original_local_path:
                task_result_id_to_local_path[task_res_id] = final_path # Update renamed map with final path
                updated_count += 1
                logging.info(f"Task {task_id}: Updated path for task result ID {task_res_id} after decompression: {final_path}") # Updated log
            elif not final_path:
                 # Should not happen if decompress_files works correctly
                 logging.error(f"Task {task_id}: Path missing from decompression map for task result ID {task_res_id}: {original_local_path}. Using original path.") # Updated log
                 task_result_id_to_local_path[task_res_id] = original_local_path # Keep original path
            # else: final_path is same as original, no update needed

        if updated_count > 0:
            logging.info(f"Task {task_id}: Finished decompression check. {updated_count} task result files were decompressed.") # Updated log
        else:
            logging.info(f"Task {task_id}: No required task result files needed decompression.") # Updated log

        # 8. Reconstruct Vars Data with Local Paths (for the container job's task)
        vars_to_update = {} # {var_name: new_data_structure_with_paths}
        for var_name, (original_data_structure, _) in required_vars_data_structure.items(): # Unpack tuple
            new_data_with_paths = []
            if not isinstance(original_data_structure, list):
                 logging.error(f"Task {task_id}: Original data structure for required var '{var_name}' is not a list ({type(original_data_structure)}). Cannot reconstruct paths.") # Updated log
                 # Store original data or None? Storing None to indicate failure.
                 vars_to_update[var_name] = None
                 continue

            # original_data_structure contains task IDs
            for element in original_data_structure:
                if isinstance(element, (int, float)):
                    try:
                        task_file_id = int(element) # Renamed file_id -> task_file_id
                        local_path = task_result_id_to_local_path.get(task_file_id) # Use renamed map
                        if local_path is None:
                             logging.error(f"Task {task_id}: Local path not found for required task result file ID {task_file_id} in var '{var_name}' after download/decompression. Adding None.") # Updated log
                        new_data_with_paths.append(local_path)
                    except (ValueError, TypeError):
                         logging.error(f"Task {task_id}: Invalid numeric task ID '{element}' encountered during path reconstruction for var '{var_name}'. Adding None.") # Updated log
                         new_data_with_paths.append(None) # Add None for invalid IDs found during reconstruction
                elif isinstance(element, list):
                    path_list = []
                    for sub_element in element:
                         if isinstance(sub_element, (int, float)):
                             try:
                                 task_file_id = int(sub_element) # Renamed file_id -> task_file_id
                                 local_path = task_result_id_to_local_path.get(task_file_id) # Use renamed map
                                 if local_path is None:
                                      logging.error(f"Task {task_id}: Local path not found for required task result file ID {task_file_id} in nested list for var '{var_name}'. Adding None.") # Updated log
                                 path_list.append(local_path)
                             except (ValueError, TypeError):
                                  logging.error(f"Task {task_id}: Invalid numeric task ID '{sub_element}' in nested list encountered during path reconstruction for var '{var_name}'. Adding None.") # Updated log
                                  path_list.append(None)
                         else:
                              logging.warning(f"Task {task_id}: Non-numeric item '{sub_element}' found in nested list during path reconstruction for var '{var_name}'. Adding None.") # Updated log
                              path_list.append(None) # Add None for non-numeric items in list
                    new_data_with_paths.append(path_list)
                else:
                     logging.warning(f"Task {task_id}: Non-numeric/non-list item '{element}' found during path reconstruction for var '{var_name}'. Adding None.") # Updated log
                     new_data_with_paths.append(None) # Add None for unexpected types

            vars_to_update[var_name] = new_data_with_paths

        # 9. Update Container Job's Task Vars List
        # Ensure self.task['vars'] and self.task['vars']['vars'] exist
        # Ensure self.task and self.task['vars'] are dicts before accessing/modifying
        if not isinstance(self.task, dict):
             raise TypeError(f"Task data (self.task) is not a dictionary when updating vars for bulk task{task_id}.") # Updated log
        if not isinstance(self.task.get('vars'), dict):
            logging.warning(f"Bulk task {task_id}: 'vars' key missing or not a dict. Initializing.") # Updated log
            self.task['vars'] = {}
        if not isinstance(self.task['vars'].get('vars'), list):
             logging.warning(f"Bulk task {task_id}: 'vars'['vars'] key missing or not a list. Initializing.") # Updated log
             self.task['vars']['vars'] = []

        vars_list = self.task['vars']['vars']
        updated_var_names = set()

        for i, var_def in enumerate(vars_list):
            if isinstance(var_def, dict) and var_def.get('name') in vars_to_update:
                var_name = var_def['name']
                new_data = vars_to_update[var_name]
                if new_data is not None: # Check if reconstruction was successful
                    logging.info(f"Bulk task {task_id}: Updating 'data' for variable '{var_name}' with local paths.") # Updated log
                    vars_list[i]['data'] = new_data
                    updated_var_names.add(var_name)
                else:
                    logging.error(f"Bulk task {task_id}: Skipping update for variable '{var_name}' due to errors during path reconstruction.") # Updated log

        # Add any processed required vars that were NOT found in the original vars_list
        vars_to_add = set(vars_to_update.keys()) - updated_var_names
        for var_name_to_add in vars_to_add:
             new_data = vars_to_update[var_name_to_add]
             if new_data is not None:
                 # Retrieve original links stored earlier
                 _, original_links = required_vars_data_structure.get(var_name_to_add, (None, [])) # Default links to []
                 new_var_def = {
                     'name': var_name_to_add,
                     'data': new_data,
                     'links': original_links # Add links from original workervar
                 }
                 vars_list.append(new_var_def)
                 logging.info(f"Bulk task {task_id}: Appended missing required variable '{var_name_to_add}' to task vars list.") # Updated log
             else:
                  logging.error(f"Bulk task {task_id}: Skipping append for variable '{var_name_to_add}' due to errors during path reconstruction.") # Updated log

        logging.info(f"Bulk task {task_id}: Finished processing required bulk input files and updating task vars.") # Updated log

    # --- Bulk Container Job Processing Methods End ---

    def _setup_storage(self):
        # This method sets up storage for a standard task or the container job task
        task_id = self.task['id'] # Renamed job_id -> task_id
        task_path = f"{task_folder}/{task_id}" # Renamed job_path -> task_path, uses task_id

        # Setup for RNXFILES (Input for standard task)
        self.rnxstoreClass = self.get_storage_class('supabase_file')
        if self.rnxstoreClass is None:
            print(f"Task {task_id}: Unknown storage type for RNXFILES") # Updated log
            return
        # Assumes input bucket_name is correct
        self.rnxstore = self.rnxstoreClass(bucket_name, task_path, "/output") # Uses task_path

        # Setup for OUTPUTFILES (Output for standard task or container job)
        self.outputstoreClass = self.get_storage_class('supabase_file')
        if self.outputstoreClass is None:
            print(f"Task {task_id}: Unknown storage type for OUTPUTFILES") # Updated log
            return
        # Uses output_bucket_name
        self.outputstore = self.outputstoreClass(output_bucket_name, task_path, "/output") # Uses task_path

        # Setup for DEVOUTPUTFILES (Dev Output for standard task or container job)
        self.devoutputstoreClass = self.get_storage_class('supabase_file')
        if self.devoutputstoreClass is None:
            print(f"Task {task_id}: Unknown storage type for DEVOUTPUTFILES") # Updated log
            return
        # Uses output_bucket_name (or dev_output_bucket_name if logic was different)
        self.devoutputstore = self.devoutputstoreClass(output_bucket_name, task_path, "/devoutput") # Uses task_path

    def _setup_output_files(self):
        """Load and setup output files from task JSON (job_json)"""
        task_id = self.task['id'] # Renamed job_id -> task_id
        task_path = f"{task_folder}/{task_id}" # Path based on task_id
        # Get process definitions from job_json
        processes = self.task.get('job_json', {}).get('process', []) # Renamed job_json -> job_json

        # Setup standard variables using task_path
        self.output_manager.set_variable('OUTPUTFOLDER', f"{task_path}/output")
        self.output_manager.set_variable('DEVOUTPUTFOLDER', f"{task_path}/devoutput")
        # self.output_manager.set_variable('DSNAME', str(task_id)) # DSNAME is now set by the frontend

        # Also set these variables in the task vars using task_path
        self.set_var(self.task['vars'], 'OUTPUTFOLDER', f"{task_path}/output")
        self.set_var(self.task['vars'], 'DEVOUTPUTFOLDER', f"{task_path}/devoutput")
        # self.set_var(self.task['vars'], 'DSNAME', str(task_id)) # DSNAME is now set by the frontend

        # Set all variables from task vars list in output manager
        task_vars_list = self.task.get('vars', {}).get('vars', []) # Renamed job_vars_list -> task_vars_list
        if isinstance(task_vars_list, list):
            for var_def in task_vars_list:
                if isinstance(var_def, dict) and 'name' in var_def and 'data' in var_def:
                    var_name = var_def['name']
                    var_value = var_def['data']
                    self.output_manager.set_variable(var_name, str(var_value))
                    logging.debug(f"Task {task_id} OutputManager: Set variable '{var_name}' = '{str(var_value)}'") # Updated log
                else:
                    logging.warning(f"Task {task_id}: Skipping invalid variable definition in task_vars_list: {var_def}") # Updated log
        else:
             logging.error(f"Task {task_id}: task['vars']['vars'] is not a list: {type(task_vars_list)}. Cannot load variables into OutputManager.") # Updated log

        # Load output files from each module defined in job_json
        for process in processes:
            args = process.get('args', {})

            # Check if module is enabled
            enable_value = args.get('enable', True)
            # If enable is a string (variable), resolve it from task vars
            if isinstance(enable_value, str) and enable_value.startswith('$') and enable_value.endswith('$'):
                var_name = enable_value[1:-1]  # Remove $ signs
                # Resolve from self.task['vars'] which should contain the resolved variables
                enable_value = self.task.get('vars', {}).get(var_name, True) # Check task vars directly

            # Skip if module is disabled
            if not enable_value:
                continue

            output_files = args.get('output_files', [])
            for file_def in output_files:
                try:
                    output_file = OutputFile(
                        path=file_def['path'],
                        filename=file_def['filename'],
                        file_type=file_def['type'],
                        visible=file_def.get('visible', True),
                        required=file_def.get('required', True)
                    )
                    self.output_manager.add_output_file(output_file)
                except KeyError as e:
                    print(f"Task {task_id}: Missing required field in output_files definition: {e}") # Updated log

    def _validate_output_files(self) -> bool:
        """Check if all required output files exist for the current task/job"""
        task_id = self.task['id'] # Use task_id
        missing_files = self.output_manager.get_missing_required_files()

        if missing_files:
            error_msg = f"Task {task_id}: Missing required output files: {', '.join(missing_files)}" # Updated log
            print(error_msg)
            self.task['result'] = { # Update self.task
                'status': 'Error',
                'error_message': error_msg
            }
            return False

        return True

    def find_var_def(self, vars_list, name):
        """Helper to find a variable definition dictionary by name."""
        if not isinstance(vars_list, list):
             logging.error(f"Cannot search for variable '{name}': vars_list is not a list.")
             return None
        for v_def in vars_list:
            # Ensure v_def is a dictionary before accessing .get()
            if isinstance(v_def, dict) and v_def.get('name') == name:
                return v_def
        return None

    def _process_files(self):
        """Processes input files for a standard task."""
        task_id = self.task['id'] # Renamed job_id -> task_id
        task_path = f"{task_folder}/{task_id}" # Renamed job_path -> task_path
        os.makedirs(task_path, exist_ok=True) # Ensure task path exists

        # Ensure workervars is a list
        if not isinstance(self.workervars, list):
             logging.error(f"workervars is not a list for task {task_id}. Cannot process files.") # Updated log
             # Decide how to handle: return error, raise exception, or continue?
             # For now, let's try to continue but log the error.
             raise ValueError(f"Task {task_id}: workervars is not a list.") # Updated log
             # self.workervars = [] # Setting to empty list might hide issues

        # Collect all file_ids from workervars (these are input files for the task)
        all_file_ids = []
        for var_info in self.workervars:
             # Ensure var_info is a dict and has 'file_ids' which is a list
             if isinstance(var_info, dict) and isinstance(var_info.get('file_ids'), list):
                 all_file_ids.extend(var_info['file_ids'])
             else:
                  logging.warning(f"Task {task_id}: Skipping invalid workervar item: {var_info}") # Updated log


        if not all_file_ids:
             logging.info(f"No file IDs found in workervars for task {task_id}. Skipping file processing.") # Updated log
             # Still set output folders
             self.set_var(self.task['vars'], 'DEVOUTPUTFOLDER', f"{task_path}/devoutput") # Use task_path
             self.set_var(self.task['vars'], 'OUTPUTFOLDER', f"{task_path}/output") # Use task_path
             # Consider if raising an error is appropriate if files were expected
             # raise ValueError(f"Task {task_id}: No file IDs found in workervars.")
             return # Exit early if no files to process

        # Fetch all file paths at once
        # Ensure all_file_ids contains only valid IDs (e.g., integers or strings expected by get_file_paths)
        valid_file_ids = [fid for fid in all_file_ids if fid is not None] # Basic check
        if not valid_file_ids:
             logging.warning(f"No valid file IDs to fetch for task {task_id}.") # Updated log
             # Still set output folders
             self.set_var(self.task['vars'], 'DEVOUTPUTFOLDER', f"{task_path}/devoutput") # Use task_path
             self.set_var(self.task['vars'], 'OUTPUTFOLDER', f"{task_path}/output") # Use task_path
             return

        try:
            # DB function MUST use old name 'file_id'
            db_start_time = self._start_timing('database_time')
            file_paths_map = database.get_file_paths(valid_file_ids) # Returns dict {file_id: path}
            self._end_timing('database_time', db_start_time)
        except Exception as e:
            logging.error(f"Failed to fetch file paths from database for task {task_id}: {e}", exc_info=True) # Updated log
            raise # Re-raise exception to signal task failure

        # Setup storage for all files (consider moving this earlier if needed)
        # Ensure bucket_name is valid before creating storage instance
        if not bucket_name:
             logging.error(f"Input bucket_name is not configured. Cannot process files for task {task_id}.") # Updated log
             raise ValueError("Input bucket configuration missing.")
        # Use task_path for local storage base
        self.filestore = storage.SuperbaseStorage(bucket_name, task_path, "/") # Use root path for downloads

        # Ensure self.task['vars'] and self.task['vars']['vars'] exist
        self.task.setdefault('vars', {}) # Use self.task
        self.task['vars'].setdefault('vars', []) # Use self.task
        task_vars_list = self.task['vars']['vars'] # Renamed job_vars_list -> task_vars_list

        # Process all files and update task JSON vars
        for var_info in self.workervars:
            # Basic validation again inside the loop
            if not isinstance(var_info, dict) or not isinstance(var_info.get('file_ids'), list):
                 continue # Skip invalid entries logged earlier

            var_name = var_info.get('name')
            if not var_name:
                 logging.warning(f"Task {task_id}: Workervar item missing 'name': {var_info}") # Updated log
                 continue

            # Get remote paths for this variable's file_ids using the map from DB
            remote_paths_for_var = [file_paths_map.get(fid) for fid in var_info['file_ids'] if file_paths_map.get(fid)]

            if not remote_paths_for_var:
                logging.warning(f"Task {task_id}: No valid remote file paths found for variable '{var_name}' with file_ids {var_info['file_ids']}") # Updated log
                continue

            # --- Download Files ---
            # Create a map for bulk download: {remote_path: unique_local_path}
            download_map = {}
            expected_local_paths_list = [] # Keep order consistent with remote_paths_for_var

            # Create a reverse mapping from remote_path to file_id for naming
            remote_path_to_file_id = {}
            for file_id in var_info['file_ids']:
                remote_path = file_paths_map.get(file_id)
                if remote_path:
                    remote_path_to_file_id[remote_path] = file_id

            for remote_path in remote_paths_for_var:
                 # Get the file_id for this remote_path to create unique filename
                 file_id = remote_path_to_file_id.get(remote_path)
                 original_filename = os.path.basename(remote_path)

                 # Create unique local filename with file_id prefix to prevent conflicts
                 if file_id is not None:
                     local_filename = f"{file_id}_{original_filename}"
                 else:
                     # Fallback to original naming if file_id not found (shouldn't happen)
                     local_filename = original_filename
                     logging.warning(f"Task {task_id}: Could not find file_id for remote_path '{remote_path}', using original filename")

                 # Sanitize if needed: safe_local_filename = "".join(c if c.isalnum() or c in ['.', '_', '-'] else '_' for c in local_filename)
                 unique_local_path = os.path.join(task_path, local_filename) # Use task_path
                 download_map[remote_path] = unique_local_path
                 expected_local_paths_list.append(unique_local_path)

            logging.info(f"Task {task_id}: Attempting to download {len(download_map)} file(s) for variable '{var_name}'...") # Updated log
            try:
                 # Time the download operation
                 download_start_time = self._start_timing('download_time')
                 # Assuming get_files_bulk takes a map and returns list of successfully downloaded *local* paths
                 downloaded_local_paths_set = set(self.filestore.get_files_bulk(download_map))
                 self._end_timing('download_time', download_start_time)
            except Exception as e:
                 logging.error(f"Task {task_id}: Error downloading files for variable '{var_name}': {e}", exc_info=True) # Updated log
                 # Decide how to handle: continue to next var, or raise error? Continuing for now.
                 continue

            # Verify downloads
            missing_local_files = set(expected_local_paths_list) - downloaded_local_paths_set
            if missing_local_files:
                 logging.error(f"Task {task_id}: Failed to download some files for variable '{var_name}'. Missing: {missing_local_files}") # Updated log
                 # Decide how to handle: continue or raise? Continuing.
                 continue

            # --- Decompression ---
            # Use the list of expected local paths for decompression check
            logging.info(f"Task {task_id}: Checking {len(expected_local_paths_list)} downloaded file(s) for decompression for '{var_name}'...") # Updated log
            try:
                 # decompress_files expects absolute paths, returns map {original_abs_path: final_abs_path}
                 decompression_map = self.decompress_files(expected_local_paths_list, task_path) # Use task_path
                 # Create the final list of local paths in the correct order
                 final_local_paths = [decompression_map.get(p, p) for p in expected_local_paths_list]
            except Exception as e:
                 logging.error(f"Task {task_id}: Error during decompression for variable '{var_name}': {e}", exc_info=True) # Updated log
                 # Decide how to handle: use original paths or fail? Using original for now.
                 final_local_paths = expected_local_paths_list


            # --- Update Task Vars ---
            var_def = self.find_var_def(task_vars_list, var_name) # Use task_vars_list

            # Determine if it's an array type based on naming convention
            is_array_var = 'A_' in var_name # Adjust this check as needed

            if var_def is None:
                logging.info(f"Task {task_id}: Variable definition for '{var_name}' not found. Creating new definition.") # Updated log
                if is_array_var:
                    new_data = final_local_paths
                else:
                    # Use the first path if available, otherwise an empty string
                    new_data = final_local_paths[0] if final_local_paths else ""

                new_var_def = {'name': var_name, 'data': new_data}
                task_vars_list.append(new_var_def) # Append to task_vars_list
                logging.info(f"Task {task_id}: Created and appended new variable definition for '{var_name}': {new_var_def}") # Updated log
                continue # Skip the rest of the update logic for this variable
            else:
                # Existing variable definition found, proceed with update logic
                logging.info(f"Task {task_id}: Found existing variable definition for '{var_name}'. Proceeding with update.") # Updated log
                current_data = var_def.get('data')
                # The existing try block starting on the next line handles the update

            try:
                if isinstance(current_data, str): # Case 1: Old Style String
                    if is_array_var:
                        logging.error(f"Task {task_id}: Data type mismatch for array variable '{var_name}': Expected list, got string.") # Updated log
                        continue
                    if not final_local_paths:
                         logging.warning(f"Task {task_id}: No local path found for single file variable '{var_name}'. Setting data to empty string.") # Updated log
                         var_def['data'] = ""
                    else:
                         var_def['data'] = final_local_paths[0]
                    logging.debug(f"Task {task_id}: Updated old-style string var '{var_name}' with path: {var_def['data']}") # Updated log

                elif isinstance(current_data, list) and all(isinstance(item, str) for item in current_data): # Case 2: Old Style List[String]
                    if not is_array_var:
                        logging.error(f"Task {task_id}: Data type mismatch for single file variable '{var_name}': Expected string/dict, got list[string].") # Updated log
                        continue
                    var_def['data'] = final_local_paths # Replace list of remote paths/placeholders with local paths
                    logging.debug(f"Task {task_id}: Updated old-style list[string] var '{var_name}' with paths: {var_def['data']}") # Updated log

                elif isinstance(current_data, dict): # Case 3: New Style Dict
                    if is_array_var:
                        logging.error(f"Task {task_id}: Data type mismatch for array variable '{var_name}': Expected list[dict], got dict.") # Updated log
                        continue
                    if not final_local_paths:
                         logging.warning(f"Task {task_id}: No local path found for single file variable '{var_name}'. Cannot update filename.") # Updated log
                    else:
                         current_data['filename'] = final_local_paths[0]
                         logging.debug(f"Task {task_id}: Updated new-style dict var '{var_name}' filename to: {current_data['filename']}") # Updated log
                         # Extract & Inject nested vars
                         for key, value in current_data.items():
                             if key not in ['filename', 'path']:
                                 logging.debug(f"Task {task_id}: Injecting nested var '{key}' from '{var_name}' as top-level var.") # Updated log
                                 # Use set_var to add/update the top-level variable in task vars
                                 self.set_var(self.task['vars'], key, value) # Use self.task

                elif isinstance(current_data, list) and all(isinstance(item, dict) for item in current_data): # Case 4: New Style List[Dict]
                    if not is_array_var:
                        logging.error(f"Task {task_id}: Data type mismatch for single file variable '{var_name}': Expected string/dict, got list[dict].") # Updated log
                        continue
                    if len(current_data) != len(final_local_paths):
                        logging.error(f"Task {task_id}: Mismatch between number of data objects ({len(current_data)}) and downloaded files ({len(final_local_paths)}) for '{var_name}'. Skipping update.") # Updated log
                        continue

                    for i, data_obj in enumerate(current_data):
                         if not isinstance(data_obj, dict): # Sanity check
                              logging.warning(f"Task {task_id}: Item {i} in list for var '{var_name}' is not a dict. Skipping.") # Updated log
                              continue
                         data_obj['filename'] = final_local_paths[i]
                         logging.debug(f"Task {task_id}: Updated new-style list[dict] var '{var_name}' item {i} filename to: {data_obj['filename']}") # Updated log
                         # Extract & Inject nested vars from this object
                         for key, value in data_obj.items():
                             if key not in ['filename', 'path']:
                                 logging.debug(f"Task {task_id}: Injecting nested var '{key}' from '{var_name}' item {i} as top-level var.") # Updated log
                                 # Use set_var to add/update the top-level variable in task vars
                                 self.set_var(self.task['vars'], key, value) # Use self.task

                else: # Case 5: Other/Unexpected or Non-File Variable
                     logging.debug(f"Task {task_id}: Variable '{var_name}' data type ({type(current_data)}) not processed as file. Assuming pass-through.") # Updated log
                     # No action needed here for pass-through, data remains as is.

            except Exception as e:
                logging.error(f"Task {task_id}: Error processing variable '{var_name}' during data update: {e}", exc_info=True) # Updated log
                # Continue to the next variable

        # Set output folders (remains the same, uses task_path)
        self.set_var(self.task['vars'], 'DEVOUTPUTFOLDER', f"{task_path}/devoutput") # Use self.task
        self.set_var(self.task['vars'], 'OUTPUTFOLDER', f"{task_path}/output") # Use self.task

    def decompress_files(self, files_to_process: list[str], folder: str) -> dict[str, str]:
        """
        Decompresses files (.gz, .Z) in the specified folder. Operates on absolute paths.

        Args:
            files_to_process (list[str]): List of absolute local file paths to potentially decompress.
            folder (str): The base folder where these files reside (used for logging/context).

        Returns:
            dict[str, str]: A map where keys are the original absolute file paths and
                            values are the final absolute paths (decompressed path or original if not compressed/error).
        """
        final_paths_map = {}
        for original_file_path in files_to_process:
            # Ensure we are working with absolute paths
            if not os.path.isabs(original_file_path):
                 logging.warning(f"Decompress received relative path, skipping: {original_file_path}")
                 final_paths_map[original_file_path] = original_file_path # Keep original if relative
                 continue

            final_path = original_file_path # Assume original path initially
            filename = os.path.basename(original_file_path)

            try:
                if not os.path.exists(original_file_path):
                    logging.warning(f"File not found for decompression, skipping: {original_file_path}")
                    final_paths_map[original_file_path] = original_file_path # Keep original if not found
                    continue

                if original_file_path.endswith(('.gz', '.GZ')):
                    decompressed_file_path = original_file_path[:-3]
                    logging.info(f"Decompressing GZ: {filename} -> {os.path.basename(decompressed_file_path)}")
                    with gzip.open(original_file_path, 'rb') as f_in, open(decompressed_file_path, 'wb') as f_out:
                        shutil.copyfileobj(f_in, f_out)
                    os.remove(original_file_path) # Remove original compressed file
                    final_path = decompressed_file_path
                elif original_file_path.endswith(('.z', '.Z')):
                    # Note: uncompress often removes the original .Z file automatically
                    decompressed_file_path = original_file_path[:-2]
                    logging.info(f"Decompressing Z: {filename} -> {os.path.basename(decompressed_file_path)}")
                    # Ensure the target doesn't exist if uncompress doesn't overwrite
                    if os.path.exists(decompressed_file_path):
                        os.remove(decompressed_file_path)
                    # Use subprocess.run for better error handling
                    result = subprocess.run(['uncompress', '-f', original_file_path], capture_output=True, text=True, check=False)
                    if result.returncode != 0:
                        error_msg = f"Failed to decompress {filename}: {result.stderr.strip()}"
                        logging.error(error_msg)
                        # Decide how to handle error: skip file? raise exception?
                        # For now, log error and keep original path in map
                        final_path = original_file_path # Keep original on error
                    else:
                         final_path = decompressed_file_path
                # else: file is not compressed, final_path remains original_file_path

                final_paths_map[original_file_path] = final_path

            except Exception as e:
                logging.error(f"Error during decompression of {filename}: {e}", exc_info=True)
                # Keep original path in map on error
                final_paths_map[original_file_path] = original_file_path

        return final_paths_map

    def _run_core(self):
        """Runs the C++ core with the task's configuration and variables."""
        task_id = self.task['id'] # Use task_id
        if SHUTDOWN_EVENT and SHUTDOWN_EVENT.is_set():
            print(f"Task {task_id}: Shutdown signal received. Exiting core processing.") # Updated log
            return {'status': 'Cancelled'}

        original_stdout = None
        original_stderr = None
        devnull = None

        try:
            # Use job_json and task vars
            job_json_str = json.dumps(self.task.get('job_json', {}), indent=4) # Renamed j -> job_json_str
            task_vars_str = json.dumps(self.task.get('vars', {}).get('vars', []), indent=4) # Renamed v -> task_vars_str
            logPath = f"{task_folder}/{task_id}/devoutput/log.log" # Use task_id, Renamed job_folder -> task_folder

            # Save template (job_json) and vars JSONs to devoutput folder
            template_path = f"{task_folder}/{task_id}/devoutput/template.json" # Use task_id, Renamed job_folder -> task_folder
            vars_path = f"{task_folder}/{task_id}/devoutput/vars.json" # Use task_id, Renamed job_folder -> task_folder

            os.makedirs(os.path.dirname(template_path), exist_ok=True)
            with open(template_path, 'w') as f:
                f.write(job_json_str) # Write job_json_str
            with open(vars_path, 'w') as f:
                f.write(task_vars_str) # Write task_vars_str

            # Only redirect output in production mode
            if not is_dev:
            #if True:    # Save original descriptors and redirect to /dev/null
                original_stdout = os.dup(1)
                original_stderr = os.dup(2)
                devnull = os.open(os.devnull, os.O_WRONLY)
                os.dup2(devnull, 1)
                os.dup2(devnull, 2)

            # Execute core and get result
            core = ctypes.CDLL(self.core_library)
            core.executejson.argtypes = [ctypes.c_char_p, ctypes.c_char_p, ctypes.c_char_p, ctypes.c_int]
            core.executejson.restype = ctypes.c_char_p
            logging.info(f"Task {task_id}: Calling C++ core executejson function...") # Updated log

            # Time the core processing operation
            processing_start_time = self._start_timing('processing_time')
            # Pass job_json and task_vars
            result_bytes = core.executejson(job_json_str.encode('utf-8'), task_vars_str.encode('utf-8'), logPath.encode('utf-8'), concurrent_threads_per_worker_core)
            self._end_timing('processing_time', processing_start_time)

            logging.info(f"Task {task_id}: C++ core executejson function returned.") # Updated log

            if not result_bytes:
                 logging.error(f"Task {task_id}: C++ core returned null pointer.") # Updated log
                 raise RuntimeError(f"Task {task_id}: C++ core returned null, indicating a potential crash or error within the core.") # Updated log

            result_string = result_bytes.decode('utf-8')
            logging.info(f"Task {task_id}: Received result string (length: {len(result_string)})") # Updated log
            result_json = json.loads(result_string)
            return result_json

        except Exception as e:
            print(f"Task {task_id}: Error executing core: {e}") # Updated log
            # Return error status specific to the task
            return {'status': 'Error', 'error_message': f"Core execution failed: {str(e)}"}

        finally:
            # Restore file descriptors if they were changed
            if all(fd is not None for fd in (original_stdout, original_stderr, devnull)):
                os.dup2(original_stdout, 1)
                os.dup2(original_stderr, 2)
                os.close(devnull)
                os.close(original_stdout)
                os.close(original_stderr)

    def _store_output_files(self):
        """Stores the output files generated by the task or container task."""
        task_id = self.task['id'] # Use task_id
        user_id = self.task['user_id'] # Use task user_id
        # Paths are based on the task_id (which could be a standard task or a container job task)
        remote_path = f"/{user_id}/results/{task_id}/"
        remote_path_devoutput = f"/{user_id}/devoutput/{task_id}/" # New path for devoutput

        # Time the upload operations
        upload_start_time = self._start_timing('upload_time')
        output_files = self.outputstore.put_files(remote_path)
        devoutput_files = self.devoutputstore.put_files(remote_path_devoutput) # Use new path
        self._end_timing('upload_time', upload_start_time)

        combined_files = output_files + devoutput_files # Combine file lists

        # Insert result files into task_results table using task_id
        # DB function MUST use old name 'job_id' (which is task_id here)
        db_start_time = self._start_timing('database_time')
        self._insert_task_results(task_id, combined_files) # Renamed, pass task_id
        self._end_timing('database_time', db_start_time)

    def _insert_task_results(self, task_id, result_files): # Renamed from _insert_task_results, param job_id -> task_id
        """Inserts task result file metadata into the database."""
        for file_path in result_files:
            file_name = os.path.basename(file_path)
            file_size = 0  # Set file size to 0 as requested
            content_type = self._get_content_type(file_name)

            # Find corresponding output file definition in the task's output manager
            output_file = None
            for output_file_def in self.output_manager.output_files:
                # Check if the resolved path (which includes task_id) ends with the filename
                if self.output_manager.get_resolved_path(output_file_def).endswith(file_name):
                    output_file = output_file_def
                    break

            # Insert with metadata if found
            # DB function MUST use old name 'job_id' (which is task_id here) and table 'task_results'
            if output_file:
                database.insert_task_result(
                    task_id=task_id, # Pass task_id as job_id
                    file_name=file_name,
                    file_path=file_path,
                    file_size=file_size,
                    content_type=content_type,
                    file_type=output_file.file_type,
                    visible=output_file.visible,
                    required=output_file.required,
                    table_name='task_results' # Keep table name
                )
            else:
                # Log warning for undefined output files
                logging.warning(f"Task {task_id}: Output file not defined in task JSON: {file_path}") # Updated log
                # Insert as unmanaged file (not visible, not required)
                # DB function MUST use old name 'job_id' (which is task_id here) and table 'task_results'
                database.insert_task_result(
                    task_id=task_id, # Pass task_id as job_id
                    file_name=file_name,
                    file_path=file_path,
                    file_size=file_size,
                    content_type=content_type,
                    file_type='unmanaged',  # Special type for undeclared files
                    visible=False,          # Hide by default
                    required=False,         # Not required by default
                    table_name='task_results' # Keep table name
                )

        # Update has_results flag in jobs table (using task_id as job_id)
        # DB function MUST use old name 'job_id' (which is task_id here) and table_name 'jobs'
        # database.update_job_has_results(task_id, True, table_name=self.table_name) # Keep commented

    def _get_content_type(self, file_name):
        # Simple content type detection based on file extension
        extension = os.path.splitext(file_name)[1].lower()
        content_types = {
            '.txt': 'text/plain',
            '.csv': 'text/csv',
            '.json': 'application/json',
            '.xml': 'application/xml',
            '.pdf': 'application/pdf',
            '.zip': 'application/zip',
            '.tix': 'application/tix',
            '.kml': 'application/vnd.google-earth.kml+xml'
        }
        return content_types.get(extension, 'application/octet-stream')

    def _finalize_task(self): # Renamed from _finalize_job
        """Finalizes a completed task (standard or container job task)."""
        task_id = self.task['id'] # Use task_id
        # 'batch_id' now refers to the container job ID (new 'job_id')
        container_job_id = self.task.get('job_id') # batch_id column in tasks table (self.task) renamed to job_id

        # DB functions MUST use old name 'job_id' (which is task_id here) and table_name 'jobs'
        db_start_time = self._start_timing('database_time')
        database.update_task_result(task_id, self.task['result'], table_name=self.table_name)
        database.set_task_status(task_id, "Completed", table_name=self.table_name)
        self._end_timing('database_time', db_start_time)

        # Check if this task was part of a container job (i.e., not a container job itself)
        # 'bulk_job_type' indicates if the task *is* a container job. If it's None/False, it's a standard task.
        is_container_job_task = self.task.get('bulk_job_type')
        if not is_container_job_task and container_job_id is not None:
            # This was a standard task belonging to a container job. Update the container job's status.
            # DB function MUST use old name 'batch_id' (which is container_job_id here)
            logging.info(f"Task {task_id} completed, updating container job (batch) {container_job_id} status.") # Added log
            db_start_time = self._start_timing('database_time')
            database.update_job_status_summary(container_job_id, "complete") # Pass container_job_id as batch_id
            self._end_timing('database_time', db_start_time)
        elif is_container_job_task:
             logging.info(f"Container job task {task_id} completed.") # Log completion of container job task
        else:
             logging.warning(f"Task {task_id} completed but has no associated container job ID (batch_id).") # Log warning if batch_id is missing for a standard task

    def _task_failed(self): # Renamed from _job_failed
        """Handles a failed task (standard or container job task)."""
        task_id = self.task['id'] # Use task_id
        # 'batch_id' now refers to the container job ID (new 'job_id')
        container_job_id = self.task.get('job_id') # batch_id column in tasks table (self.task) renamed to job_id

        # DB functions MUST use old name 'job_id' (which is task_id here) and table_name 'jobs'
        db_start_time = self._start_timing('database_time')
        database.update_task_result(task_id, self.task['result'], table_name=self.table_name)
        database.set_task_status(task_id, "Failed", table_name=self.table_name)
        self._end_timing('database_time', db_start_time)

        # Check if this task was part of a container job (i.e., not a container job itself)
        is_container_job_task = self.task.get('bulk_job_type')
        if not is_container_job_task and container_job_id is not None:
            # This was a standard task belonging to a container job. Update the container job's status to error.
            # DB function MUST use old name 'batch_id' (which is container_job_id here)
            logging.info(f"Task {task_id} failed, updating container job (batch) {container_job_id} status to error.") # Added log
            db_start_time = self._start_timing('database_time')
            database.update_job_status_summary(container_job_id, "error") # Renamed update_batch_status -> update_job_status_summary
            self._end_timing('database_time', db_start_time)
        elif is_container_job_task:
             logging.info(f"Container job task {task_id} failed.") # Log failure of container job task
        else:
             logging.warning(f"Task {task_id} failed but has no associated container job ID (batch_id).") # Log warning if batch_id is missing

    def get_precision_files(self, task_vars_json): # Renamed json -> task_vars_json
        """Extracts precision file paths (Clock/SP3) from task variables."""
        # 'vars' here refers to the list of variable definitions within the task's 'vars' structure
        vars_list = task_vars_json.get('vars') # Renamed vars -> vars_list
        clkfiles = []
        sp3files = []
        if vars_list:
            for var_def in vars_list: # Renamed var -> var_def
                if isinstance(var_def, dict): # Check if item is a dictionary
                    if var_def.get('name') == 'CLOCKFILES':
                        clkfiles = var_def.get('data', []) # Default to empty list
                    elif var_def.get('name') == 'SP3FILES':
                        sp3files = var_def.get('data', []) # Default to empty list
        # Ensure clkfiles and sp3files are lists before concatenating
        if not isinstance(clkfiles, list): clkfiles = []
        if not isinstance(sp3files, list): sp3files = []
        # Merge clkfiles and sp3files into one array
        files = clkfiles + sp3files
        return files if files else False # Return False if empty, otherwise the list

    def get_clock_files_from_array(self, files):
        """Filters a list of file paths to get only clock files."""
        clock_files = []
        if not isinstance(files, list): return clock_files # Return empty if input is not a list
        for file_path in files: # Renamed file -> file_path
            if isinstance(file_path, str) and ('CLK' in file_path or 'clk' in file_path): # Check type
                clock_files.append(file_path)
        return clock_files

    def get_orb_files_from_array(self, files):
        """Filters a list of file paths to get only orbit (SP3/EPH) files."""
        orb_files = []
        if not isinstance(files, list): return orb_files # Return empty if input is not a list
        for file_path in files: # Renamed file -> file_path
             if isinstance(file_path, str) and ('SP3' in file_path or 'EPH' in file_path or 'sp3' in file_path or 'eph' in file_path): # Check type
                orb_files.append(file_path)
        return orb_files

    def get_rnx_files(self, task_config_json): # Renamed json -> task_config_json
        """Extracts RNX file paths from the task configuration JSON."""
        processes = task_config_json.get('process')
        if processes:
            for process in processes:
                if process.get('name') == 'GNSS-Preprocessor':
                    args = process.get('args')
                    if args:
                        rinexfiles = args.get('rnxfiles')
                        # Ensure rinexfiles is returned, even if None or empty list
                        return rinexfiles
        return False # Return False if not found

    def set_output_files_to_local_path(self, task_config_json, path, devpath): # Renamed json -> task_config_json
        """Updates output file paths within the task configuration JSON to local paths."""
        processes = task_config_json.get('process')
        if processes:
            for process in processes:
                args = process.get('args')
                if args:
                    output_file = args.get('out')
                    if output_file and isinstance(output_file, str): # Check if output_file exists and is a string
                        # Use the appropriate path (output or devoutput) based on some logic?
                        # Currently, it seems to always use 'path'. Assuming 'path' is the main output path.
                        # If differentiation is needed, more logic is required here.
                        args['out'] = os.path.join(path, os.path.basename(output_file))
                        # Example if devpath was needed for certain types:
                        # if output_file.endswith('.log'):
                        #     args['out'] = os.path.join(devpath, os.path.basename(output_file))
                        # else:
                        #     args['out'] = os.path.join(path, os.path.basename(output_file))
        return task_config_json

    def get_storage_class(self, storage_type):
        """Returns the storage class based on the type string."""
        if storage_type == 'supabase_file':
            return storage.SuperbaseStorage
        elif storage_type == 'local_storage':
            return storage.LocalStorage
        else:
            logging.error(f"Unknown storage type requested: {storage_type}") # Added log
            return None

    def set_var(self, task_vars_container, name, data): # Renamed json -> task_vars_container
        """Sets or updates a variable definition in the task's 'vars' structure."""
        # Expects task_vars_container to be the dictionary containing the 'vars' list, e.g., self.task['vars']
        if not isinstance(task_vars_container, dict):
             logging.error(f"Cannot set variable '{name}': Provided container is not a dictionary.")
             return task_vars_container # Return unchanged

        vars_list = task_vars_container.get('vars') # Renamed vars -> vars_list
        # Initialize 'vars' list if it doesn't exist or is not a list
        if not isinstance(vars_list, list):
            task_vars_container['vars'] = []
            vars_list = task_vars_container['vars']

        var_exists = False
        for var_def in vars_list: # Renamed var -> var_def
            if isinstance(var_def, dict) and var_def.get('name') == name: # Check if item is dict
                var_def['data'] = data
                var_exists = True
                break

        if not var_exists:
            vars_list.append({'name': name, 'data': data})

        return task_vars_container # Return the modified container
